# 酒店 API 分销平台开发计划

## 项目概览

基于当前代码库分析，本项目是一个酒店 API
分销平台，采用微服务架构，包含用户服务、搜索服务、订单服务、内容服务、地理服务、规则引擎、BI & 监控工具等核心模块。

## 开发计划

### 第一阶段：高优先级基础设施特性

#### 基础代码

- [x] **定义国家常量**:
  参考 [ISO_3166-1二位字母代码](https://zh.wikipedia.org/wiki/ISO_3166-1%E4%BA%8C%E4%BD%8D%E5%AD%97%E6%AF%8D%E4%BB%A3%E7%A0%81)
- [x] **定义币种常量**: 在 money 包中定义币种常量(三字码），参考 [ISO_4217](https://zh.wikipedia.org/wiki/ISO_4217)
- [x] **汇率转换缓存层**:  优化`money.ExchangeService`, 实现缓存功能，减少外部调用次数
- [x] **确保所有 json 都是驼峰命名风格
- [ ] **trade 模块domain 层**： 避免 service 层直接引用 model！dao 层（mysql 包）应该处理好 converter 逻辑，对外暴露
  domain.Order等数据模型，而非 dao层（mysql 包）的数据模型。
- [x] **trade/cancel** 模块要创建 refund_order表记录，并完成相关状态流转、金额留档
_- [ ] 所有标记 openapi 的接口及其数据模型(_
  Region,Regions,HotelList,HotelRates,CheckAvail,Book,Cancel,QueryOrders,QueryOrder等），自动添加 AI
  注释，并要求注释中不能出现中文（中文可以用//apidoc:zh来指示 apidoc
  生成命令不导出）

#### 审计日志

- [x] 后端审计日志查询接口升级增强
- [x] 前端审计日志查看子菜单

#### 酒店列表页

- [x] 
    1. Available Suppliers Only 按钮无法点击；
- [x] 
    2. 多语言问题：供应商下拉框未支持英文；
- [x] 
    3. 酒店评分模块，rating 字样无任何意义，如果没有数据的话应该隐藏，包括星级；
- [x] 
    4. 最低价格为 0 的话，也应该隐藏；
- [x] 
    5. 酒店首图功能，选择携程可以看到是有图片数据的，前端应该实现；
- [x] 
    6. facilities 功能补齐，携程的酒店也是有数据的，现在没有展示。
- [x] 
    7. 多语言问题：酒店卡片的hotelname，可以看到切换中文后，酒店名没有切换展示为中文。
- [x] 
    8. 多语言问题：filter 模块的酒店名称和最低价格，无英文支持
- [x] 
    9. 计数问题：Found 0 hotels，没有返回酒店总数。先移除该 toast功能。
- [x] 
    10. 翻页问题：前端需要根据后端的 hasMore参数来判断翻页，以支持无限滚动。
- [x] 
    11. supplier_x_id新增支持 30个供应商（当前仅支持 20个）

#### mapping模块
- [x] giata 的两个 client 应该合并，GiataClient和 GiataRoomMappingClient

#### Notification System（通知系统）

- [x] **后端实现**
    - [x] 扩展现有 `notify/` 模块功能
    - [x] 实现多渠道通知（邮件、短信、推送）
    - [x] 添加通知模板管理
    - [x] 实现通知历史记录
    - [ ] 编写相关单元测试
- [ ] **前端实现**
    - [ ] 在 `admin-fe/src/views/system/` 下创建 `notifications/` 目录
    - [ ] 实现通知管理页面
    - [ ] 添加通知模板编辑器
    - [ ] 实现通知发送历史查看
- [ ] **集成测试**
    - [ ] 测试各种通知渠道
    - [ ] 验证通知模板功能

### 第二阶段：合规性和安全特性

#### GDPR Compliance（GDPR 合规）

- [ ] **数据隐私实现**
    - [ ] 在 `user/service/` 下实现数据导出功能
    - [ ] 实现数据删除（被遗忘权）功能
    - [ ] 添加数据处理同意管理
    - [ ] 实现数据匿名化处理
- [ ] **前端合规界面**
    - [ ] 在 `admin-fe/src/views/compliance/` 下创建 GDPR 管理页面
    - [ ] 实现用户数据导出界面
    - [ ] 添加数据删除申请处理界面
    - [ ] 实现同意管理界面
- [ ] **审计日志**
    - [ ] 扩展现有 BI 日志系统
    - [ ] 添加 GDPR 相关操作记录
    - [ ] 实现合规报告生成

#### Customer Credit Limit（客户信用额度）

- [ ] **信用管理后端**
    - [ ] 在 `user/service/` 下实现信用额度管理
    - [ ] 在 `trade/service/` 下集成信用检查
    - [ ] 实现信用额度监控和预警
    - [ ] 添加信用历史记录
- [ ] **前端信用管理**
    - [ ] 在 `admin-fe/src/views/finance/` 下创建信用管理页面
    - [ ] 实现信用额度设置界面
    - [ ] 添加信用使用监控仪表板
    - [ ] 实现信用预警通知

### 第三阶段：运维和优化特性

#### Session Data Cleanup（会话数据清理）

- [ ] **数据清理实现**
    - [ ] 在 `user/service/` 下实现会话清理逻辑
    - [ ] 添加定时清理任务
    - [ ] 实现清理策略配置
    - [ ] 添加清理统计和报告
- [ ] **监控和管理**
    - [ ] 在 `admin-fe/src/views/system/` 下添加会话管理页面
    - [ ] 实现清理策略配置界面
    - [ ] 添加清理统计展示

#### AWS Portal Access（AWS 门户访问）

- [ ] **AWS 集成**
    - [ ] 扩展现有 AWS SDK 集成
    - [ ] 实现 AWS 资源管理接口
    - [ ] 添加 AWS 服务监控
    - [ ] 实现成本分析功能
- [ ] **管理界面**
    - [ ] 在 `admin-fe/src/views/aws/` 下创建 AWS 管理页面
    - [ ] 实现资源监控仪表板
    - [ ] 添加成本分析界面
    - [ ] 实现服务状态监控

## 技术实施建议

### 开发规范

- [ ] 遵循现有代码结构和命名规范
- [ ] 使用 TDD 开发方式，先写测试再写实现
- [ ] 确保所有新功能都有完整的单元测试覆盖
- [ ] 前端组件复用现有设计系统和组件库

### 质量保证

- [ ] 每个功能完成后进行代码审查
- [ ] 运行完整的测试套件确保无回归
- [ ] 进行性能测试和安全测试
- [ ] 更新相关文档和 API 文档

### 部署和监控

- [ ] 使用现有的 Docker 和 CI/CD 流程
- [ ] 集成到现有的监控和日志系统
- [ ] 配置适当的告警和通知
- [ ] 准备回滚计划

## 预期成果

### 功能完整性

- [ ] 所有基础设施特性完全实现
- [ ] 前后端功能完整集成
- [ ] 用户体验流畅一致

### 技术质量

- [ ] 代码质量高，可维护性强
- [ ] 测试覆盖率达到 80% 以上
- [ ] 性能满足业务需求
- [ ] 安全性符合行业标准

### 运维支持

- [ ] 完善的监控和告警机制
- [ ] 详细的操作文档和故障排查指南
- [ ] 自动化的部署和回滚流程

---

**注意事项：**

- 每个阶段完成后需要进行全面测试
- 优先级可根据业务需求调整
- 建议每个功能模块由专门的 AI agent 负责开发
- 定期进行代码审查和架构评估